#  أنت:
# اكتب بلغة الرينج برنامج لادارة المخزون 
# جاري المعالجة...
#  المساعد:
# ## برنامج إدارة المخزون بلغة الرينج

# Language: ring
# تعريف صنف المنتج
Class Product
   name = ""
   price = 0
   quantity = 0

   # دالة البناء
   Func init(name, price, quantity)
      me.name = name
      me.price = price
      me.quantity = quantity
   End

   # دالة عرض معلومات المنتج
   Func Display()
      See "اسم المنتج: " + name + nl
      See "السعر: " + price + nl
      See "الكمية: " + quantity + nl
   End
End

# تعريف صنف المخزن
Class Inventory
   products = []

   # دالة إضافة منتج
   Func AddProduct(product)
      add(products, product)
   End

   # دالة إزالة منتج
   Func RemoveProduct(name)
      for p in products
         if p.name = name
            del(products, p)
            return
         ok
      next
      See "المنتج غير موجود" + nl
   End

   # دالة البحث عن منتج
   Func FindProduct(name)
      for p in products
         if p.name = name
            p.Display()
            return
         ok
      next
      See "المنتج غير موجود" + nl
   End

   # دالة عرض جميع المنتجات
   Func DisplayAllProducts()
      for p in products
         p.Display()
      next
   End

   # دالة تحديث كمية منتج
   Func UpdateQuantity(name, quantity)
      for p in products
         if p.name = name
            p.quantity = quantity
            return
         ok
      next
      See "المنتج غير موجود" + nl
   End
End

# إنشاء مخزن جديد
inventory = New Inventory()

# حلقة البرنامج الرئيسية
while true
   See "1. إضافة منتج" + nl
   See "2. إزالة منتج" + nl
   See "3. البحث عن منتج" + nl
   See "4. عرض جميع المنتجات" + nl
   See "5. تحديث كمية منتج" + nl
   See "6. الخروج" + nl
   See "اختر رقماً:" + nl

   choice = GetInput()

   switch choice
   on 1
      See "أدخل اسم المنتج:" + nl
      name = GetInput()
      See "أدخل سعر المنتج:" + nl
      price = Number(GetInput())
      See "أدخل كمية المنتج:" + nl
      quantity = Number(GetInput())
      product = New Product(name, price, quantity)
      inventory.AddProduct(product)
   on 2
      See "أدخل اسم المنتج:" + nl
      name = GetInput()
      inventory.RemoveProduct(name)
   on 3
      See "أدخل اسم المنتج:" + nl
      name = GetInput()
      inventory.FindProduct(name)
   on 4
      inventory.DisplayAllProducts()
   on 5
      See "أدخل اسم المنتج:" + nl
      name = GetInput()
      See "أدخل الكمية الجديدة:" + nl
      quantity = Number(GetInput())
      inventory.UpdateQuantity(name, quantity)
   on 6
      exit
   other
      See "اختيار خاطئ" + nl
   end
end

# دالة استقبال مدخلات من المستخدم
Func GetInput()
   give input
   return input

# **شرح البرنامج:**
# *   **صنف المنتج `Product`:**
#     *   يُعرّف خصائص المنتج مثل الاسم والسعر والكمية.
#     *   يحتوي على دالة البناء `New` لإنشاء كائن جديد من نوع `Product`.
#     *   يحتوي على دالة `Display` لعرض معلومات المنتج.
# *   **صنف المخزن `Inventory`:**
#     *   يُعرّف مصفوفة `products` لتخزين المنتجات.
#     *   يحتوي على دوال لإضافة منتج `AddProduct`، وإزالة منتج `RemoveProduct`، والبحث عن منتج `FindProduct`، وعرض جميع المنتجات `DisplayAllProducts`، وتحديث كمية منتج `UpdateQuantity`.
# *   **حلقة البرنامج الرئيسية:**
#     *   تعرض قائمة بالخيارات للمستخدم.
#     *   تستقبل مدخلات من المستخدم باستخدام دالة `GetInput`.
#     *   تنفذ العملية المطلوبة بناءً على اختيار المستخدم.
# *   **دالة `GetInput`:**
#     *   تستقبل مدخلات من المستخدم وتعيدها كسلسلة نصية.
# **ملاحظات:**
# *   هذا البرنامج هو مثال بسيط لبرنامج إدارة المخزون.
# *   يمكن تحسين البرنامج بإضافة المزيد من الميزات مثل:
#     *   حفظ البيانات في ملف.
#     *   إضافة واجهة مستخدم رسومية.
#     *   إضافة تقارير.
# *   تم استخدام اللغة العربية في كتابة التعليقات وأسماء المتغيرات لجعل البرنامج أكثر وضوحاً للمبرمجين العرب.
# *   تأكد من تنصيب لغة الرينج على جهازك قبل تشغيل البرنامج.
# **كيفية تشغيل البرنامج:**
# 1. احفظ الكود في ملف باسم `inventory.ring`.
# 2. افتح موجه الأوامر (Command Prompt) أو الطرفية (Terminal).
# 3. انتقل إلى المسار الذي حفظت فيه الملف.
# 4. اكتب الأمر `ring inventory.ring` واضغط على Enter.
# سيتم تشغيل البرنامج وعرض قائمة بالخيارات. اختر رقماً من القائمة واتبع التعليمات.

